<script setup>
import Screenfull from "@/components/Screenfull/index.vue"
import SearchMenu from "@/components/SearchMenu/index.vue"
import ThemeSwitch from "@/components/ThemeSwitch/index.vue"
import { useDevice } from "@/hooks/useDevice"
import { useLayoutMode } from "@/hooks/useLayoutMode"
import { useAppStore } from "@/store/modules/app"
import { useSettingsStore } from "@/store/modules/settings"
import { useUserStore } from "@/store/modules/user"
import { Breadcrumb, Hamburger, Sidebar } from "../index"
import { getNotificationListApi, markNotificationReadApi, changePasswordApi } from "@/apis/notification"
import hasNotifyIcon from "@/assets/images/message/has-notify.png"
import noNotifyIcon from "@/assets/images/message/no-notify.png"

const { isMobile } = useDevice()

const { isTop } = useLayoutMode()

const router = useRouter()

const appStore = useAppStore()

const userStore = useUserStore()

const settingsStore = useSettingsStore()

const { showThemeSwitch, showScreenfull, showSearchMenu } = storeToRefs(settingsStore)

// 通知相关状态
const notifications = ref([])
const hasUnreadNotifications = ref(false)
const notificationLoading = ref(false)

// 修改密码相关状态
const changePasswordDialogVisible = ref(false)
const changePasswordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})
const changePasswordFormRef = useTemplateRef('changePasswordFormRef')

// 修改密码表单验证规则
const changePasswordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20位', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/,
      message: '密码必须包含字母和数字',
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== changePasswordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
}

/** 切换侧边栏 */
function toggleSidebar() {
  appStore.toggleSidebar(false)
}

/** 登出 */
function logout() {
  userStore.logout()
  router.push("/login")
}

/** 获取通知列表 */
async function fetchNotifications() {
  try {
    notificationLoading.value = true
    const { data } = await getNotificationListApi()
    notifications.value = data.list || []
    hasUnreadNotifications.value = notifications.value.some(item => !item.isRead)
  } catch (error) {
    console.error('获取通知列表失败:', error)
    // 模拟数据
    notifications.value = [
      {
        id: 1,
        title: '您有 1 条新的待审核问题',
        content: '您有 1 条新的待审核问题',
        createTime: '2024-12-16 15:22:49',
        isRead: false
      },
      {
        id: 2,
        title: '您有 1 条新的待分办问题',
        content: '您有 1 条新的待分办问题',
        createTime: '2024-12-16 15:22:49',
        isRead: false
      },
      {
        id: 3,
        title: '您有 1 条新的督办件问题',
        content: '您有 1 条新的督办件问题',
        createTime: '2024-12-16 15:22:49',
        isRead: false
      },
      {
        id: 4,
        title: '您有 1 条新的审核未通过问题',
        content: '您有 1 条新的审核未通过问题',
        createTime: '2024-12-16 15:22:49',
        isRead: true
      },
      {
        id: 5,
        title: '您有 1 条新的督办件问题',
        content: '您有 1 条新的督办件问题',
        createTime: '2024-12-16 15:22:49',
        isRead: true
      }
    ]
    hasUnreadNotifications.value = notifications.value.some(item => !item.isRead)
  } finally {
    notificationLoading.value = false
  }
}

/** 标记通知为已读 */
async function markAsRead(notification) {
  if (notification.isRead) return

  try {
    await markNotificationReadApi(notification.id)
    notification.isRead = true
    hasUnreadNotifications.value = notifications.value.some(item => !item.isRead)
    ElMessage.success('标记为已读')
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('标记已读失败')
  }
}

/** 打开修改密码弹窗 */
function openChangePasswordDialog() {
  changePasswordForm.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
  changePasswordDialogVisible.value = true
}

/** 关闭修改密码弹窗 */
function closeChangePasswordDialog() {
  changePasswordDialogVisible.value = false
  changePasswordFormRef.value?.resetFields()
}

/** 提交修改密码 */
async function submitChangePassword() {
  try {
    await changePasswordFormRef.value.validate()

    await changePasswordApi({
      oldPassword: changePasswordForm.value.oldPassword,
      newPassword: changePasswordForm.value.newPassword
    })

    ElMessage.success('密码修改成功，请重新登录')
    closeChangePasswordDialog()

    // 修改密码成功后登出
    setTimeout(() => {
      logout()
    }, 1500)
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('修改密码失败')
  }
}

// 组件挂载时获取通知列表
onMounted(() => {
  fetchNotifications()
})
</script>

<template>
  <div class="navigation-bar">
    <!-- <Breadcrumb v-if="!isTop || isMobile" class="breadcrumb" />
    <Sidebar v-if="isTop && !isMobile" class="sidebar" /> -->
    <div class="right-menu">
      <!-- <SearchMenu v-if="showSearchMenu" class="right-menu-item" />
      <Screenfull v-if="showScreenfull" class="right-menu-item" />
      <ThemeSwitch v-if="showThemeSwitch" class="right-menu-item" /> -->

      <!-- 系统通知 -->
      <el-popover
        placement="bottom"
        :width="360"
        trigger="hover"
        popper-class="notification-popover"
      >
        <template #reference>
          <div class="right-menu-item notification-icon">
            <img
              :src="hasUnreadNotifications ? hasNotifyIcon : noNotifyIcon"
              alt="通知"
              width="24"
              height="24"
            >
          </div>
        </template>

        <div class="notification-panel">
          <div class="notification-header">
            <span class="notification-title">系统通知</span>
          </div>

          <div class="notification-list" v-loading="notificationLoading">
            <div
              v-for="notification in notifications"
              :key="notification.id"
              class="notification-item"
              :class="{ 'unread': !notification.isRead }"
              @click="markAsRead(notification)"
            >
              <div class="notification-content">
                <div class="notification-text">{{ notification.content }}</div>
                <div class="notification-time">{{ notification.createTime }}</div>
              </div>
              <div v-if="!notification.isRead" class="unread-dot"></div>
            </div>

            <div v-if="notifications.length === 0" class="no-notifications">
              暂无通知
            </div>
          </div>
        </div>
      </el-popover>

      <!-- 用户下拉菜单 -->
      <el-dropdown>
        <div class="right-menu-item user">
          <img src="@/assets/images/layouts/default-avatar.png" alt="" width="24" height="24">
          <span class="pl-[8px]">{{ userStore.username }}</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="openChangePasswordDialog">
              修改密码
            </el-dropdown-item>
            <el-dropdown-item @click="logout">
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 修改密码弹窗 -->
    <el-dialog
      v-model="changePasswordDialogVisible"
      title="修改密码"
      width="480px"
      class="custom-dialog"
      :before-close="closeChangePasswordDialog"
    >
      <el-form
        ref="changePasswordFormRef"
        :model="changePasswordForm"
        :rules="changePasswordRules"
        label-position="top"
        class="reset-password-form"
      >
        <el-form-item label="原密码" prop="oldPassword" required>
          <el-input
            v-model="changePasswordForm.oldPassword"
            type="password"
            placeholder="请输入原密码"
            show-password
            maxlength="20"
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword" required>
          <el-input
            v-model="changePasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
            maxlength="20"
          />
        </el-form-item>

        <el-form-item label="确认新密码" prop="confirmPassword" required>
          <el-input
            v-model="changePasswordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
            maxlength="20"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="closeChangePasswordDialog">取消</el-button>
          <el-button class="confirm-btn" @click="submitChangePassword">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.navigation-bar {
  height: var(--v3-navigationbar-height);
  overflow: hidden;
  color: var(--v3-navigationbar-text-color);
  display: flex;
  justify-content: flex-end;

  .hamburger {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 15px;
    cursor: pointer;
  }
  .breadcrumb {
    flex: 1;
    // 参考 Bootstrap 的响应式设计将宽度设置为 576
    @media screen and (max-width: 576px) {
      display: none;
    }
  }
  .sidebar {
    flex: 1;
    // 设置 min-width 是为了让 Sidebar 里的 el-menu 宽度自适应
    min-width: 0px;
    :deep(.el-menu) {
      background-color: transparent;
    }
    :deep(.el-sub-menu) {
      &.is-active {
        .el-sub-menu__title {
          color: var(--el-color-primary);
        }
      }
    }
  }
  .right-menu {
    margin-right: 10px;
    height: 100%;
    display: flex;
    align-items: center;
    &-item {
      margin: 0 10px;
      cursor: pointer;
      &:last-child {
        margin-left: 20px;
      }
    }
    .notification-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      transition: background-color 0.3s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
    .user {
      display: flex;
      align-items: center;
      .el-avatar {
        margin-right: 10px;
      }
      span {
        font-size: 16px;
      }
    }
  }
}

// 通知面板样式
:deep(.notification-popover) {
  padding: 0 !important;

  .notification-panel {
    .notification-header {
      padding: 16px 20px 12px;
      border-bottom: 1px solid #f0f0f0;

      .notification-title {
        font-size: 16px;
        font-weight: 600;
        color: #2B2C33;
      }
    }

    .notification-list {
      max-height: 300px;
      overflow-y: auto;

      .notification-item {
        padding: 12px 20px;
        border-bottom: 1px solid #f8f8f8;
        cursor: pointer;
        position: relative;
        transition: background-color 0.3s;

        &:hover {
          background-color: #f8f9fa;
        }

        &.unread {
          background-color: #f0f8ff;
        }

        &:last-child {
          border-bottom: none;
        }

        .notification-content {
          .notification-text {
            font-size: 14px;
            color: #2B2C33;
            line-height: 20px;
            margin-bottom: 4px;
          }

          .notification-time {
            font-size: 12px;
            color: #999;
          }
        }

        .unread-dot {
          position: absolute;
          top: 16px;
          right: 20px;
          width: 8px;
          height: 8px;
          background-color: #ff4d4f;
          border-radius: 50%;
        }
      }

      .no-notifications {
        padding: 40px 20px;
        text-align: center;
        color: #999;
        font-size: 14px;
      }
    }
  }
}

// 重置密码表单样式
.reset-password-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      color: var(--grey1);
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .el-input__wrapper {
      height: 48px;
      font-size: 16px;
    }
  }
}
</style>
