import { request } from "@/utils/axios"

/** 获取登录验证码 */
export function getCaptchaApi() {
  return request({
    url: "auth/captcha",
    method: "get"
  })
}

/** 发送手机验证码 */
export function sendSmsCodeApi(data) {
  return request({
    url: "auth/sms-code",
    method: "post",
    data
  })
}

/** 重置密码 */
export function resetPasswordApi(data) {
  return request({
    url: "auth/reset-password",
    method: "post",
    data
  })
}

/** 登录并返回 Token */
export function loginApi(data) {
  return request({
    url: "auth/login",
    method: "post",
    data
  })
}
